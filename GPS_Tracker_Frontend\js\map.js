// 地图管理类
class GPSMap {
    constructor(containerId) {
        this.containerId = containerId;
        this.map = null;
        this.currentMarker = null;
        this.trackPolyline = null;
        this.navigationPolyline = null;
        this.destinationMarker = null;
        this.trackPoints = [];
        this.isAutoCenter = CONFIG.APP.AUTO_CENTER;
        this.showTrack = CONFIG.APP.SHOW_TRACK;
        
        this.initMap();
        this.setupEventListeners();
    }

    // 初始化地图
    initMap() {
        try {
            // 创建地图实例
            this.map = L.map(this.containerId, {
                center: CONFIG.MAP.DEFAULT_CENTER,
                zoom: CONFIG.MAP.DEFAULT_ZOOM,
                maxZoom: CONFIG.MAP.MAX_ZOOM,
                minZoom: CONFIG.MAP.MIN_ZOOM,
                zoomControl: true
            });

            // 添加OpenStreetMap图层
            const osmLayer = L.tileLayer(
                CONFIG.MAP.TILE_LAYERS.OPENSTREETMAP.url,
                {
                    attribution: CONFIG.MAP.TILE_LAYERS.OPENSTREETMAP.attribution,
                    maxZoom: CONFIG.MAP.MAX_ZOOM
                }
            );

            // 添加卫星图层
            const satelliteLayer = L.tileLayer(
                CONFIG.MAP.TILE_LAYERS.SATELLITE.url,
                {
                    attribution: CONFIG.MAP.TILE_LAYERS.SATELLITE.attribution,
                    maxZoom: CONFIG.MAP.MAX_ZOOM
                }
            );

            // 默认使用OpenStreetMap
            osmLayer.addTo(this.map);

            // 添加图层控制
            const baseLayers = {
                "OpenStreetMap": osmLayer,
                "卫星图": satelliteLayer
            };
            L.control.layers(baseLayers).addTo(this.map);

            // 添加比例尺
            L.control.scale({
                metric: true,
                imperial: false,
                position: 'bottomleft'
            }).addTo(this.map);

            log('地图初始化成功', 'success');

        } catch (error) {
            log(`地图初始化失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 地图点击事件
        this.map.on('click', (e) => {
            const { lat, lng } = e.latlng;
            log(`地图点击: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
        });

        // 地图缩放事件
        this.map.on('zoomend', () => {
            log(`地图缩放级别: ${this.map.getZoom()}`);
        });
    }

    // 更新当前位置
    updateCurrentLocation(gpsData) {
        if (!gpsData.latitude || !gpsData.longitude) {
            log('GPS数据无效', 'warn');
            return;
        }

        const { latitude, longitude, altitude, timestamp } = gpsData;
        const position = [latitude, longitude];

        // 更新或创建当前位置标记
        if (this.currentMarker) {
            this.currentMarker.setLatLng(position);
        } else {
            // 创建自定义图标
            const customIcon = L.divIcon({
                className: 'custom-marker',
                html: `<div style="background: #e74c3c; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3);"></div>`,
                iconSize: [26, 26],
                iconAnchor: [13, 13]
            });

            this.currentMarker = L.marker(position, { icon: customIcon })
                .addTo(this.map);
        }

        // 更新标记弹窗
        const popupContent = `
            <div style="text-align: center;">
                <h4>🚗 ${CONFIG.APP.DEVICE.NAME}</h4>
                <p><strong>位置:</strong> ${Utils.formatCoordinate(latitude)}, ${Utils.formatCoordinate(longitude)}</p>
                ${altitude ? `<p><strong>海拔:</strong> ${altitude.toFixed(1)}m</p>` : ''}
                <p><strong>时间:</strong> ${Utils.formatTime(timestamp)}</p>
                <p><a href="${Utils.generateMapLink(latitude, longitude)}" target="_blank">在OpenStreetMap中查看</a></p>
            </div>
        `;
        this.currentMarker.bindPopup(popupContent);

        // 处理导航数据
        if (gpsData.navigation) {
            this.updateNavigationRoute(gpsData.navigation, position);
        }

        // 添加到轨迹
        if (this.showTrack) {
            this.addTrackPoint(position, timestamp);
        }

        // 自动居中
        if (this.isAutoCenter) {
            this.centerOnLocation(position);
        }

        log(`位置更新: ${Utils.formatCoordinate(latitude)}, ${Utils.formatCoordinate(longitude)}`, 'success');
    }

    // 添加轨迹点
    addTrackPoint(position, timestamp) {
        // 添加新点
        this.trackPoints.push({
            position: position,
            timestamp: timestamp
        });

        // 限制轨迹点数量
        if (this.trackPoints.length > CONFIG.APP.MAX_TRACK_POINTS) {
            this.trackPoints.shift();
        }

        // 更新轨迹线
        this.updateTrackPolyline();
    }

    // 更新轨迹线
    updateTrackPolyline() {
        if (this.trackPoints.length < 2) return;

        const positions = this.trackPoints.map(point => point.position);

        if (this.trackPolyline) {
            this.trackPolyline.setLatLngs(positions);
        } else {
            this.trackPolyline = L.polyline(positions, {
                color: '#3498db',
                weight: 4,
                opacity: 0.8,
                smoothFactor: 1
            }).addTo(this.map);

            // 添加轨迹信息
            this.trackPolyline.bindPopup(`
                <div>
                    <h4>📍 移动轨迹</h4>
                    <p>轨迹点数: ${this.trackPoints.length}</p>
                    <p>开始时间: ${Utils.formatTime(this.trackPoints[0].timestamp)}</p>
                    <p>最新时间: ${Utils.formatTime(this.trackPoints[this.trackPoints.length - 1].timestamp)}</p>
                </div>
            `);
        }
    }

    // 居中到指定位置
    centerOnLocation(position, zoom = null) {
        const targetZoom = zoom || this.map.getZoom();
        this.map.setView(position, targetZoom);
    }

    // 清除轨迹
    clearTrack() {
        if (this.trackPolyline) {
            this.map.removeLayer(this.trackPolyline);
            this.trackPolyline = null;
        }
        this.trackPoints = [];
        log('轨迹已清除', 'success');
    }

    // 设置自动居中
    setAutoCenter(enabled) {
        this.isAutoCenter = enabled;
        log(`自动居中: ${enabled ? '开启' : '关闭'}`);
    }

    // 设置轨迹显示
    setShowTrack(enabled) {
        this.showTrack = enabled;
        if (!enabled) {
            this.clearTrack();
        }
        log(`轨迹显示: ${enabled ? '开启' : '关闭'}`);
    }

    // 加载历史轨迹
    loadHistoryTrack(historyData) {
        if (!historyData || historyData.length === 0) {
            log('没有历史数据可加载', 'warn');
            return;
        }

        // 清除现有轨迹
        this.clearTrack();

        // 添加历史轨迹点
        historyData.forEach(data => {
            if (data.latitude && data.longitude) {
                this.addTrackPoint([data.latitude, data.longitude], data.timestamp);
            }
        });

        // 如果有轨迹点，居中显示
        if (this.trackPoints.length > 0) {
            const bounds = L.latLngBounds(this.trackPoints.map(p => p.position));
            this.map.fitBounds(bounds, { padding: [20, 20] });
        }

        log(`加载了${this.trackPoints.length}个历史轨迹点`, 'success');
    }

    // 全屏切换
    toggleFullscreen() {
        const mapContainer = document.getElementById(this.containerId).parentElement;
        
        if (!document.fullscreenElement) {
            mapContainer.requestFullscreen().then(() => {
                // 全屏后重新调整地图大小
                setTimeout(() => {
                    this.map.invalidateSize();
                }, 100);
                log('进入全屏模式', 'success');
            });
        } else {
            document.exitFullscreen().then(() => {
                // 退出全屏后重新调整地图大小
                setTimeout(() => {
                    this.map.invalidateSize();
                }, 100);
                log('退出全屏模式', 'success');
            });
        }
    }

    // 获取地图实例
    getMap() {
        return this.map;
    }

    // 更新导航路径
    updateNavigationRoute(navigationData, currentPosition) {
        try {
            log('处理导航数据:', navigationData);

            // 清除之前的导航路径
            if (this.navigationPolyline) {
                this.map.removeLayer(this.navigationPolyline);
                this.navigationPolyline = null;
            }

            // 清除之前的目的地标记
            if (this.destinationMarker) {
                this.map.removeLayer(this.destinationMarker);
                this.destinationMarker = null;
            }

            // 检查导航数据类型
            if (navigationData.type === 'navigation' && navigationData.destination) {
                // 根据目的地名称确定坐标
                let destinationCoords = null;

                if (navigationData.destination === 'wanda' || navigationData.destination === 'wangda') {
                    destinationCoords = [26.8854880000002, 112.6660550000003]; // 酃湖万达广场
                } else if (navigationData.destination === 'gaotie') {
                    destinationCoords = [26.8990, 112.6720]; // 衡阳东高铁站
                } else if (navigationData.destination === 'daxue') {
                    destinationCoords = [26.8812, 112.6769]; // 衡阳师范学院
                }

                if (destinationCoords) {
                    // 创建导航路径（简单直线，实际应用中可以调用路径规划API）
                    const routePoints = [currentPosition, destinationCoords];

                    // 绘制导航路径
                    this.navigationPolyline = L.polyline(routePoints, {
                        color: '#2196F3',
                        weight: 4,
                        opacity: 0.8,
                        dashArray: '10, 5'
                    }).addTo(this.map);

                    // 添加目的地标记
                    this.destinationMarker = L.marker(destinationCoords, {
                        icon: L.divIcon({
                            className: 'destination-marker',
                            html: `<div style="background: #4CAF50; width: 24px; height: 24px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">🎯</div>`,
                            iconSize: [30, 30],
                            iconAnchor: [15, 15]
                        })
                    }).addTo(this.map);

                    // 设置目的地标记弹窗
                    const destPopup = `
                        <div style="text-align: center;">
                            <h4>🎯 导航目的地</h4>
                            <p><strong>目的地:</strong> ${this.getDestinationName(navigationData.destination)}</p>
                            <p><strong>坐标:</strong> ${Utils.formatCoordinate(destinationCoords[0])}, ${Utils.formatCoordinate(destinationCoords[1])}</p>
                            <p><strong>距离:</strong> ${navigationData.distance ? navigationData.distance + 'm' : '计算中...'}</p>
                            <p><strong>路径点:</strong> ${navigationData.waypoints || 'N/A'}</p>
                        </div>
                    `;
                    this.destinationMarker.bindPopup(destPopup);

                    // 调整地图视图以显示整个路径
                    const bounds = L.latLngBounds([currentPosition, destinationCoords]);
                    this.map.fitBounds(bounds, { padding: [20, 20] });

                    log(`导航路径已显示: ${this.getDestinationName(navigationData.destination)}`, 'success');
                }
            }

        } catch (error) {
            log(`导航数据处理失败: ${error.message}`, 'error');
        }
    }

    // 获取目的地中文名称
    getDestinationName(destination) {
        const names = {
            'wanda': '酃湖万达广场',
            'wangda': '酃湖万达广场',
            'gaotie': '衡阳东高铁站',
            'daxue': '衡阳师范学院'
        };
        return names[destination] || destination;
    }

    // 清除导航路径
    clearNavigationRoute() {
        if (this.navigationPolyline) {
            this.map.removeLayer(this.navigationPolyline);
            this.navigationPolyline = null;
        }

        if (this.destinationMarker) {
            this.map.removeLayer(this.destinationMarker);
            this.destinationMarker = null;
        }

        log('导航路径已清除', 'success');
    }
}

// 导出地图类
window.GPSMap = GPSMap;
