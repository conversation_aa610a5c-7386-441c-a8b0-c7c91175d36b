/**
 * 路径规划模块 - 基于OpenStreetMap的免费路径规划
 * 支持OSRM和GraphHopper等免费API服务
 * 无需API密钥，符合交通规则的路径规划
 */

class RoutingService {
    constructor() {
        this.currentRoute = null;
        this.routeLayer = null;
        this.startMarker = null;
        this.endMarker = null;
        
        // 路径规划服务配置
        this.services = [
            {
                name: 'OSRM',
                url: 'https://router.project-osrm.org/route/v1/driving/',
                description: '开源路径规划服务（推荐）',
                enabled: true
            },
            {
                name: 'GraphHopper',
                url: 'https://graphhopper.com/api/1/route',
                description: 'GraphHopper免费API',
                enabled: false // 需要API key，暂时禁用
            }
        ];
        
        // 预设目的地（衡阳市主要地标）
        this.destinations = {
            'wanda': { lat: 26.8869, lon: 112.6758, name: '酃湖万达广场' },
            'gaotie': { lat: 26.8945, lon: 112.6123, name: '衡阳东高铁站' },
            'yiyuan': { lat: 26.8756, lon: 112.6234, name: '南华大学附属第一医院' },
            'huochezhan': { lat: 26.8834, lon: 112.6167, name: '衡阳火车站' },
            'daxue': { lat: 26.8812, lon: 112.6769, name: '衡阳师范学院' },
            'shangchang': { lat: 26.8823, lon: 112.6145, name: '步步高购物中心' },
            'gongyuan': { lat: 26.8934, lon: 112.5967, name: '石鼓公园' }
        };
    }
    
    /**
     * 使用OSRM进行路径规划
     */
    async planRouteWithOSRM(startLat, startLon, endLat, endLon, profile = 'driving') {
        try {
            console.log(`🚗 使用OSRM进行${profile}路径规划...`);
            
            const url = `https://router.project-osrm.org/route/v1/${profile}/${startLon},${startLat};${endLon},${endLat}?overview=full&geometries=geojson&steps=true&annotations=true`;
            
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                const route = data.routes[0];
                console.log(`✅ OSRM路径规划成功: ${(route.distance/1000).toFixed(1)}km, ${Math.round(route.duration/60)}分钟`);
                
                return {
                    coordinates: route.geometry.coordinates,
                    distance: route.distance,
                    duration: route.duration,
                    steps: this.processOSRMSteps(route.legs[0].steps),
                    provider: 'OSRM',
                    profile: profile,
                    success: true
                };
            } else {
                throw new Error(`OSRM返回错误: ${data.message || '未知错误'}`);
            }
        } catch (error) {
            console.error(`❌ OSRM路径规划失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 处理OSRM步骤数据
     */
    processOSRMSteps(steps) {
        return steps.map((step, index) => {
            let instruction = step.maneuver.instruction || `步骤 ${index + 1}`;
            
            // 翻译常见的英文指令
            const translations = {
                'Head': '出发',
                'Turn left': '左转',
                'Turn right': '右转',
                'Continue': '继续直行',
                'Arrive': '到达目的地',
                'Roundabout': '环岛',
                'Fork': '岔路口',
                'Merge': '汇入'
            };
            
            for (const [en, zh] of Object.entries(translations)) {
                if (instruction.includes(en)) {
                    instruction = instruction.replace(en, zh);
                }
            }
            
            return {
                instruction: instruction,
                distance: step.distance,
                duration: step.duration,
                type: step.maneuver.type
            };
        });
    }
    
    /**
     * 创建直线路径（备选方案）
     */
    createStraightLineRoute(startLat, startLon, endLat, endLon) {
        const distance = this.calculateDistance(startLat, startLon, endLat, endLon);
        const duration = distance / 35 * 3600; // 假设35km/h平均速度
        
        console.log(`⚠️ 使用直线路径: ${distance.toFixed(1)}km`);
        
        return {
            coordinates: [[startLon, startLat], [endLon, endLat]],
            distance: distance * 1000,
            duration: duration,
            steps: [
                { instruction: '从起点出发', distance: distance * 1000, duration: duration },
                { instruction: '直行到达目的地', distance: 0, duration: 0 }
            ],
            provider: '直线路径',
            profile: 'straight',
            success: true
        };
    }
    
    /**
     * 计算两点间距离（Haversine公式）
     */
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // 地球半径（公里）
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }
    
    /**
     * 智能路径规划（尝试多个服务）
     */
    async planRoute(startLat, startLon, endLat, endLon, profile = 'driving') {
        console.log(`🎯 开始路径规划: (${startLat.toFixed(6)}, ${startLon.toFixed(6)}) → (${endLat.toFixed(6)}, ${endLon.toFixed(6)})`);
        
        // 首先尝试OSRM（最可靠的免费服务）
        let routeData = await this.planRouteWithOSRM(startLat, startLon, endLat, endLon, profile);
        
        // 如果OSRM失败，使用直线路径作为备选
        if (!routeData.success) {
            console.log('⚠️ OSRM失败，使用直线路径...');
            routeData = this.createStraightLineRoute(startLat, startLon, endLat, endLon);
        }
        
        this.currentRoute = routeData;
        return routeData;
    }
    
    /**
     * 在地图上显示路径
     */
    displayRoute(map, routeData) {
        // 清除之前的路径
        this.clearRoute(map);
        
        if (!routeData || !routeData.coordinates) {
            console.error('❌ 无效的路径数据');
            return false;
        }
        
        // 转换坐标格式（经度,纬度 → 纬度,经度）
        const latLngs = routeData.coordinates.map(coord => [coord[1], coord[0]]);
        
        // 创建路径线
        this.routeLayer = L.polyline(latLngs, {
            color: '#2E8B57',
            weight: 5,
            opacity: 0.8,
            dashArray: routeData.provider === '直线路径' ? '10, 5' : null
        }).addTo(map);
        
        // 添加起点标记
        const startPoint = latLngs[0];
        this.startMarker = L.marker(startPoint, {
            icon: L.divIcon({
                className: 'route-marker start-marker',
                html: '🚗',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            })
        }).addTo(map).bindPopup('🚗 起点');
        
        // 添加终点标记
        const endPoint = latLngs[latLngs.length - 1];
        this.endMarker = L.marker(endPoint, {
            icon: L.divIcon({
                className: 'route-marker end-marker',
                html: '🏁',
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            })
        }).addTo(map).bindPopup('🏁 终点');
        
        // 调整地图视野以显示完整路径
        map.fitBounds(this.routeLayer.getBounds(), { padding: [20, 20] });
        
        console.log(`✅ 路径显示完成，使用${routeData.provider}服务`);
        return true;
    }
    
    /**
     * 清除路径
     */
    clearRoute(map) {
        if (this.routeLayer) {
            map.removeLayer(this.routeLayer);
            this.routeLayer = null;
        }
        
        if (this.startMarker) {
            map.removeLayer(this.startMarker);
            this.startMarker = null;
        }
        
        if (this.endMarker) {
            map.removeLayer(this.endMarker);
            this.endMarker = null;
        }
        
        this.currentRoute = null;
        console.log('🧹 路径已清除');
    }
    
    /**
     * 获取目的地信息
     */
    getDestination(key) {
        return this.destinations[key.toLowerCase()] || null;
    }
    
    /**
     * 获取所有目的地
     */
    getAllDestinations() {
        return this.destinations;
    }
    
    /**
     * 获取当前路径信息
     */
    getCurrentRoute() {
        return this.currentRoute;
    }
    
    /**
     * 格式化路径信息
     */
    formatRouteInfo(routeData) {
        if (!routeData) return null;
        
        return {
            distance: `${(routeData.distance / 1000).toFixed(1)} 公里`,
            duration: `${Math.round(routeData.duration / 60)} 分钟`,
            provider: routeData.provider,
            profile: routeData.profile || 'driving',
            steps: routeData.steps || []
        };
    }
}

// 导出路径规划服务
window.RoutingService = RoutingService;
