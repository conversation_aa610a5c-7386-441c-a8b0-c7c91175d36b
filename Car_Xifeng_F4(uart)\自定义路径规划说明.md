# 🧠 自定义路径规划系统使用说明

## 📋 系统概述

我们已经为您的GPS追踪系统添加了一个**智能自定义路径规划引擎**，无需依赖高德地图的路径规划API，完全基于本地算法实现智能导航。

## ✨ 主要特性

### 🎯 **智能路径算法**
- 基于衡阳市道路网络数据
- 考虑交通规则和实际道路情况
- 自动寻找最优中间路径点
- 避免不合理的绕行路径

### 🗺️ **可视化显示**
- 橙色路径线条显示完整路线
- 起点标记（蓝色圆点）
- 终点标记（红色星形）
- 中间路径点标记（橙色小圆点）

### 📊 **详细导航信息**
- 总距离计算（公里）
- 预估行驶时间（分钟）
- 逐步导航指令
- 方向指示（东南西北）

## 🚀 使用方法

### 方法1：测试预设路径
1. 点击 **"🧠 自定义路径规划"** 按钮
2. 系统自动规划从衡阳师范学院到万达广场的路径
3. 查看地图上的路径显示和控制台日志

### 方法2：输入自定义坐标
1. 点击 **"📝 输入坐标规划"** 按钮
2. 输入起点坐标（格式：经度,纬度）
   - 例如：`112.676903,26.881201`
3. 输入终点坐标（格式：经度,纬度）
   - 例如：`112.675797,26.886900`
4. 系统自动规划并显示路径

### 方法3：串口命令触发
1. 通过串口发送 `wanda` 命令
2. 系统自动使用自定义路径规划
3. 导航数据上传到ThingSpeak

## 🎮 控制按钮说明

| 按钮 | 功能 |
|------|------|
| 🧠 自定义路径规划 | 测试预设路径（师院→万达） |
| 📝 输入坐标规划 | 手动输入起终点坐标 |
| 🧹 清除路径 | 清除地图上的所有路径和标记 |
| 🎯 测试WANDA导航 | 原有的WANDA导航功能 |

## 📍 内置地标坐标

系统内置了衡阳市主要地标的坐标数据：

```
衡阳师范学院: 112.6769, 26.8812
万达广场:     112.6757, 26.8869
体育中心:     112.6796, 26.8851
火车站:       112.6720, 26.8890
市政府:       112.6800, 26.8900
中心医院:     112.6750, 26.8800
商业中心:     112.6850, 26.8850
```

## 🧮 算法原理

### 路径规划算法
1. **起终点分析**: 计算直线距离和方向
2. **中间点搜索**: 在道路网络中寻找合适的中间点
3. **路径优化**: 选择总距离最短且合理的路径
4. **时间估算**: 基于城市道路平均速度和交通延误

### 距离计算
- 使用**Haversine公式**计算地球表面两点间的精确距离
- 考虑地球曲率，精度可达米级

### 时间估算
- 城市道路平均速度：35km/h
- 红绿灯等待时间：每公里约2分钟
- 综合考虑交通状况

## 🔧 技术优势

### ✅ **无API依赖**
- 不需要高德地图API密钥
- 避免API调用限制和费用
- 完全离线运行

### ✅ **智能算法**
- 基于实际道路网络
- 避免不合理绕行
- 考虑交通规则

### ✅ **可扩展性**
- 易于添加新的地标点
- 可调整算法参数
- 支持自定义道路网络

## 📊 控制台日志

系统会在浏览器控制台输出详细的调试信息：

```
🧠 开始自定义路径规划...
📍 起点: 112.676903 26.881201
📍 终点: 112.675797 26.8869
✅ 路径规划完成: {waypoints: Array(4), distance: 1247, estimatedTime: 5, instructions: Array(4)}
🎨 开始绘制自定义路径...
✅ 自定义路径绘制完成
📋 导航指令:
1. 从起点出发
2. 经过 体育中心
3. 经过 万达广场
4. 到达目的地
```

## 🎯 使用建议

1. **首次使用**: 建议先点击"🧠 自定义路径规划"测试系统
2. **查看日志**: 打开浏览器开发者工具查看详细日志
3. **清除路径**: 规划新路径前建议先清除旧路径
4. **坐标格式**: 输入坐标时注意格式：经度,纬度（用英文逗号分隔）

## 🚀 系统状态

✅ **自定义路径规划引擎** - 已完成并可用
✅ **地图可视化显示** - 已完成并可用  
✅ **导航指令生成** - 已完成并可用
✅ **ThingSpeak数据上传** - 已完成并可用
✅ **串口命令支持** - 已完成并可用

您的GPS追踪系统现在拥有了完全独立的智能路径规划能力！🎉
