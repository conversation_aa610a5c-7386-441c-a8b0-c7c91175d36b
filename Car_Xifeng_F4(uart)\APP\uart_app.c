#include "uart_app.h"
#include "navigation_app.h"
#include "esp01_app.h"

#define GPS_UPLOAD_INTERVAL 10000  // GPS上传间隔（毫秒）

extern uint8_t uart_rx_dma_buffer[BUFFER_SIZE];
extern uint8_t ring_buffer_input[BUFFER_SIZE];
extern struct rt_ringbuffer ring_buffer;
extern uint8_t uart_data_buffer[BUFFER_SIZE];

void Uart_Init(void)
{
  rt_ringbuffer_init(&ring_buffer, ring_buffer_input, BUFFER_SIZE);
  HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, BUFFER_SIZE);
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);

  // 只发送一次简单的启动提示
  static uint8_t init_done = 0;
  if (!init_done) {
    HAL_Delay(500);
    my_printf(&huart1, "System Ready. Type 'hello' to test.\r\n");
    init_done = 1;
  }
}


void Uart_Task(void)
{
  uint16_t uart_data_len = rt_ringbuffer_data_len(&ring_buffer);

  // 每5秒输出一次心跳信息，确认任务在运行
  static uint32_t last_heartbeat = 0;
  if (HAL_GetTick() - last_heartbeat > 5000) {
    my_printf(&huart1, "UART Task Heartbeat - Ring buffer len: %d\r\n", uart_data_len);
    last_heartbeat = HAL_GetTick();
  }

  // DMA状态检查已移除，串口输入正常工作

  if(uart_data_len > 0)
  {
    // 有数据时LED闪烁
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);
    my_printf(&huart1, "*** DATA DETECTED! Buffer len: %d ***\r\n", uart_data_len);

    rt_ringbuffer_get(&ring_buffer, uart_data_buffer, uart_data_len);
    uart_data_buffer[uart_data_len] = '\0';

    // 临时启用调试输出来排查问题
    my_printf(&huart1, "RX: [%s] len=%d\r\n", uart_data_buffer, uart_data_len);

    // 清理输入数据，移除换行符
    char clean_command[64];
    strncpy(clean_command, (char*)uart_data_buffer, sizeof(clean_command) - 1);
    clean_command[sizeof(clean_command) - 1] = '\0';

    // 移除所有可能的换行符和回车符
    char* newline = strchr(clean_command, '\r');
    if (newline) *newline = '\0';
    newline = strchr(clean_command, '\n');
    if (newline) *newline = '\0';

    // 调试输出清理后的命令
    my_printf(&huart1, "Clean CMD: [%s] len=%d\r\n", clean_command, strlen(clean_command));

    // 优先处理导航命令
    if (strcmp(clean_command, "wangda") == 0 || strcmp(clean_command, "wanda") == 0)
    {
        my_printf(&huart1, "\r\n🎯 ========== 启动OpenStreetMap导航到酃湖万达 ==========\r\n");
        my_printf(&huart1, "🚗 目的地：酃湖万达广场\r\n");
        my_printf(&huart1, "📍 坐标：26.8869°N, 112.6758°E\r\n");
        my_printf(&huart1, "🛣️ 路线：使用OSRM免费路径规划服务\r\n");
        my_printf(&huart1, "🗺️ 正在规划路线并上传到OpenStreetMap...\r\n");
        my_printf(&huart1, "🆓 无需API密钥，完全免费使用\r\n");
        my_printf(&huart1, "==========================================\r\n\r\n");

        Navigation_StartNavigation("wanda");
        return;
    }
    else if (strcmp(clean_command, "hello") == 0)
    {
        my_printf(&huart1, "System Hello Response!\r\n");
        HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);
    }
    else if (strcmp(clean_command, "esp_status") == 0)
    {
        my_printf(&huart1, "\r\n========== ESP01 GPS Tracking Status ==========\r\n");

        ESP01_State_t state = esp01_GetState();
        my_printf(&huart1, "📡 ESP01 State: ");
        switch(state)
        {
            case ESP01_STATE_IDLE:
                my_printf(&huart1, "IDLE (Ready to start)\r\n");
                break;
            case ESP01_STATE_INIT:
                my_printf(&huart1, "INITIALIZING (Connecting to WiFi)\r\n");
                break;
            case ESP01_STATE_CONNECTING:
                my_printf(&huart1, "CONNECTING (WiFi connection in progress)\r\n");
                break;
            case ESP01_STATE_CONNECTED:
                my_printf(&huart1, "CONNECTED ✅ (Ready for GPS upload)\r\n");
                break;
            case ESP01_STATE_ERROR:
                my_printf(&huart1, "ERROR ❌ (Need reset)\r\n");
                break;
            default:
                my_printf(&huart1, "UNKNOWN\r\n");
                break;
        }

        my_printf(&huart1, "🌐 WiFi Network: Tenda_ZC_5G\r\n");
        my_printf(&huart1, "📊 ThingSpeak Channel: 3014831\r\n");
        my_printf(&huart1, "⏰ Upload Interval: 10 seconds\r\n");
        my_printf(&huart1, "🗺️ Live Map: https://687f369dfe095025e29a2697--relaxed-zabaione-3eabb4.netlify.app/\r\n");
        my_printf(&huart1, "===============================================\r\n\r\n");

        if (state == ESP01_STATE_CONNECTED) {
            my_printf(&huart1, "✅ System ready! GPS data will upload automatically.\r\n");
            my_printf(&huart1, "💡 Use 'gps_upload' to test manual upload\r\n");
        } else if (state == ESP01_STATE_IDLE) {
            my_printf(&huart1, "🚀 Use 'esp_start' to begin GPS tracking\r\n");
        }
        my_printf(&huart1, "\r\n");
    }
    else if (strcmp(clean_command, "esp_reset") == 0)
    {
        my_printf(&huart1, "Resetting ESP-01...\r\n");
        esp01_Reset();
    }
    else if (strcmp(clean_command, "esp_force_at") == 0)
    {
        Uart2_Printf(&huart2, "AT\r\n");
    }
    else if (strcmp(clean_command, "esp_test") == 0)
    {
        Uart2_Printf(&huart2, "TEST123\r\n");
    }
    else if (strcmp(clean_command, "esp_start") == 0)
    {
        esp01_StartInit();
    }
    else if (strcmp(clean_command, "esp_diag") == 0)
    {
        my_printf(&huart1, "Running ESP01 network diagnostics...\r\n");
        esp01_NetworkDiagnostics();
    }
    else if (strcmp(clean_command, "esp_force_reset") == 0)
    {
        my_printf(&huart1, "Force resetting ESP01 module...\r\n");
        esp01_ForceReset();
    }
    else if (strcmp(clean_command, "esp_quick_test") == 0)
    {
        my_printf(&huart1, "Running ESP01 quick connection test...\r\n");
        esp01_QuickConnectionTest();
    }
    else if (strcmp(clean_command, "esp_debug") == 0)
    {
        my_printf(&huart1, "Starting ESP01 debug mode...\r\n");
        esp01_DebugMode();
    }
    else if (strcmp(clean_command, "test_thingspeak") == 0)
    {
        my_printf(&huart1, "🧪 测试ThingSpeak上传...\r\n");
        my_printf(&huart1, "📡 频道: 3014831\r\n");
        my_printf(&huart1, "🔑 API密钥: LU22ZUP4ZTFK4IY9\r\n");

        // 关闭之前的连接
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(2000);

        // 连接到ThingSpeak
        my_printf(&huart1, "🔗 连接到api.thingspeak.com...\r\n");
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"api.thingspeak.com\",80\r\n");
        HAL_Delay(8000);

        // 构建测试请求 - 使用衡阳师范学院坐标
        char test_request[256];
        snprintf(test_request, sizeof(test_request),
                "GET /update?api_key=LU22ZUP4ZTFK4IY9&field1=26.8812&field2=112.6769&field3=65.0 HTTP/1.1\r\n"
                "Host: api.thingspeak.com\r\n"
                "User-Agent: ESP01-GPS-Test\r\n"
                "Connection: close\r\n\r\n");

        int request_len = strlen(test_request);
        my_printf(&huart1, "📤 发送测试数据 (%d字节)...\r\n", request_len);
        Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", request_len);
        HAL_Delay(3000);
        Uart2_Printf(&huart2, "%s", test_request);
        HAL_Delay(5000);

        // 关闭连接
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(2000);

        my_printf(&huart1, "✅ ThingSpeak测试完成\r\n");
        my_printf(&huart1, "🌐 查看结果: https://thingspeak.com/channels/3014831\r\n");
    }
    else if (strcmp(clean_command, "esp_tcp_test") == 0)
    {
        my_printf(&huart1, "Testing TCP connection...\r\n");
        if (esp01_EstablishTCPConnection()) {
            my_printf(&huart1, "✅ TCP connection test successful\r\n");
        } else {
            my_printf(&huart1, "❌ TCP connection test failed\r\n");
        }
    }
    else if (strcmp(clean_command, "esp_ip_test") == 0)
    {
        my_printf(&huart1, "Testing TCP connection with IP address...\r\n");
        if (esp01_TryTCPWithIP()) {
            my_printf(&huart1, "✅ IP connection test successful\r\n");
        } else {
            my_printf(&huart1, "❌ IP connection test failed\r\n");
        }
    }
    else if (strcmp(clean_command, "test_simple_http") == 0)
    {
        my_printf(&huart1, "Testing simple HTTP request to ThingSpeak...\r\n");
        // 发送简单的HTTP GET请求测试
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"api.thingspeak.com\",80\r\n");
        HAL_Delay(5000);
        Uart2_Printf(&huart2, "AT+CIPSEND=50\r\n");
        HAL_Delay(2000);
        Uart2_Printf(&huart2, "GET /channels/3014831 HTTP/1.1\r\nHost: api.thingspeak.com\r\n\r\n");
    }
    else if (strcmp(clean_command, "test_thingspeak_api") == 0)
    {
        my_printf(&huart1, "Testing ThingSpeak API with your credentials...\r\n");
        my_printf(&huart1, "Channel: 3014831\r\n");
        my_printf(&huart1, "Write API Key: LU22ZUP4ZTFK4IY9\r\n");
        my_printf(&huart1, "Attempting direct API call...\r\n");

        // 测试ThingSpeak API
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"api.thingspeak.com\",80\r\n");
        HAL_Delay(5000);

        char test_request[200];
        snprintf(test_request, sizeof(test_request),
                "GET /update?api_key=LU22ZUP4ZTFK4IY9&field1=26.8812&field2=112.6769&field3=65.0 HTTP/1.1\r\n"
                "Host: api.thingspeak.com\r\n\r\n");

        Uart2_Printf(&huart2, "AT+CIPSEND=%d\r\n", strlen(test_request));
        HAL_Delay(2000);
        Uart2_Printf(&huart2, "%s", test_request);
    }
    else if (strcmp(clean_command, "test_baidu") == 0)
    {
        my_printf(&huart1, "Testing connection to Baidu (domestic site)...\r\n");
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"www.baidu.com\",80\r\n");
        HAL_Delay(5000);
        Uart2_Printf(&huart2, "AT+CIPSEND=50\r\n");
        HAL_Delay(2000);
        Uart2_Printf(&huart2, "GET / HTTP/1.1\r\nHost: www.baidu.com\r\n\r\n");
    }
    else if (strcmp(clean_command, "check_network") == 0)
    {
        my_printf(&huart1, "Comprehensive network check...\r\n");
        my_printf(&huart1, "1. Checking WiFi status...\r\n");
        Uart2_Printf(&huart2, "AT+CWJAP?\r\n");
        HAL_Delay(2000);

        my_printf(&huart1, "2. Checking IP address...\r\n");
        Uart2_Printf(&huart2, "AT+CIFSR\r\n");
        HAL_Delay(2000);

        my_printf(&huart1, "3. Testing domestic DNS...\r\n");
        Uart2_Printf(&huart2, "AT+CIPDOMAIN=\"www.baidu.com\"\r\n");
        HAL_Delay(3000);

        my_printf(&huart1, "4. Testing international DNS...\r\n");
        Uart2_Printf(&huart2, "AT+CIPDOMAIN=\"api.thingspeak.com\"\r\n");
        HAL_Delay(3000);
    }
    else if (strcmp(clean_command, "send_location") == 0)
    {
        my_printf(&huart1, "📡 Uploading GPS location to ThingSpeak...\r\n");
        esp01_SendLocationData();
    }
    else if (strcmp(clean_command, "gps_upload") == 0)
    {
        my_printf(&huart1, "🚀 Starting GPS upload test...\r\n");
        esp01_UploadGPSData();
    }
    else if (strcmp(clean_command, "get_location") == 0)
    {
        float lat, lon, alt;
        esp01_GetRealLocation(&lat, &lon, &alt);
        my_printf(&huart1, "📍 当前位置: %.6f°N, %.6f°E, %.1fm\r\n", lat, lon, alt);
        my_printf(&huart1, "🗺️ OpenStreetMap: https://www.openstreetmap.org/?mlat=%.6f&mlon=%.6f&zoom=16\r\n", lat, lon);
        my_printf(&huart1, "📡 ThingSpeak频道: https://thingspeak.com/channels/3014831\r\n");
    }
    else if (strcmp(clean_command, "clean_connection") == 0)
    {
        my_printf(&huart1, "🧹 清理ESP01连接状态...\r\n");
        my_printf(&huart1, "🔄 强制关闭所有TCP连接...\r\n");
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(3000);

        my_printf(&huart1, "🔍 检查连接状态...\r\n");
        Uart2_Printf(&huart2, "AT+CIPSTATUS\r\n");
        HAL_Delay(2000);

        my_printf(&huart1, "✅ 连接状态已清理，可以进行新的连接\r\n");
        my_printf(&huart1, "💡 提示: 现在可以使用 gps_upload 或 test_thingspeak 命令\r\n");
    }
    else if (strcmp(clean_command, "gps_status") == 0)
    {
        my_printf(&huart1, "=== GPS Status Report ===\r\n");
        my_printf(&huart1, "Global GPS Data:\r\n");
        my_printf(&huart1, "  Latitude: %.6f\r\n", g_LatAndLongData.latitude);
        my_printf(&huart1, "  Longitude: %.6f\r\n", g_LatAndLongData.longitude);
        my_printf(&huart1, "  Direction: %c%c\r\n", g_LatAndLongData.N_S, g_LatAndLongData.E_W);
        my_printf(&huart1, "Save_Data Status:\r\n");
        my_printf(&huart1, "  isGetData: %d\r\n", Save_Data.isGetData);
        my_printf(&huart1, "  isParseData: %d\r\n", Save_Data.isParseData);
        my_printf(&huart1, "  isUsefull: %d\r\n", Save_Data.isUsefull);
        my_printf(&huart1, "  UTC Time: %s\r\n", Save_Data.UTCTime);
        my_printf(&huart1, "========================\r\n");
    }
    else if (strcmp(clean_command, "wanda") == 0)
    {
        my_printf(&huart1, "🗺️ 开始导航到酃湖万达广场...\r\n");

        // 获取当前GPS位置
        float current_lat, current_lon, current_alt;
        esp01_GetRealLocation(&current_lat, &current_lon, &current_alt);

        // 万达广场坐标 (与navigation_app.c中定义一致)
        float wanda_lat = 26.8869f;
        float wanda_lon = 112.6758f;

        my_printf(&huart1, "📍 当前位置: %.6f°N, %.6f°E\r\n", current_lat, current_lon);
        my_printf(&huart1, "🎯 目的地: 酃湖万达广场 (%.6f°N, %.6f°E)\r\n", wanda_lat, wanda_lon);

        // 计算距离 (简单的直线距离)
        float distance = Navigation_CalculateDistance(current_lat, current_lon, wanda_lat, wanda_lon);
        my_printf(&huart1, "📏 直线距离: %.0f米\r\n", distance);

        // 构建导航数据 (高德API格式: 经度在前，纬度在后)
        char route_data[128];
        snprintf(route_data, sizeof(route_data), "WANDA_%.6f_%.6f_%.6f_%.6f",
                current_lon, current_lat, wanda_lon, wanda_lat);

        my_printf(&huart1, "📡 发送导航数据到地图: %s\r\n", route_data);

        // 发送导航数据到前端地图
        esp01_SendNavigationData(route_data);

        my_printf(&huart1, "✅ 导航已启动！请查看地图显示路线\r\n");
        my_printf(&huart1, "🌐 地图链接: https://687f487cfe09505bd19a25c6--relaxed-zabaione-3eabb4.netlify.app/\r\n");
    }
    else if (strcmp(clean_command, "net_check") == 0)
    {
        my_printf(&huart1, "🔍 开始网络连接诊断...\r\n");
        my_printf(&huart1, "========================\r\n");

        // 1. 检查WiFi连接状态
        my_printf(&huart1, "1️⃣ 检查WiFi连接状态...\r\n");
        Uart2_Printf(&huart2, "AT+CWJAP?\r\n");
        HAL_Delay(3000);

        // 2. 检查IP地址
        my_printf(&huart1, "2️⃣ 检查IP地址...\r\n");
        Uart2_Printf(&huart2, "AT+CIFSR\r\n");
        HAL_Delay(3000);

        // 3. 测试DNS解析
        my_printf(&huart1, "3️⃣ 测试DNS解析...\r\n");
        Uart2_Printf(&huart2, "AT+CIPDOMAIN=\"api.thingspeak.com\"\r\n");
        HAL_Delay(5000);

        // 4. 测试简单连接
        my_printf(&huart1, "4️⃣ 测试ThingSpeak连接...\r\n");
        Uart2_Printf(&huart2, "AT+CIPSTART=\"TCP\",\"api.thingspeak.com\",80\r\n");
        HAL_Delay(8000);

        // 5. 关闭连接
        my_printf(&huart1, "5️⃣ 关闭测试连接...\r\n");
        Uart2_Printf(&huart2, "AT+CIPCLOSE\r\n");
        HAL_Delay(2000);

        my_printf(&huart1, "✅ 网络诊断完成\r\n");
        my_printf(&huart1, "========================\r\n");
    }
    else if (strcmp(clean_command, "gps_upload") == 0)
    {
        my_printf(&huart1, "🚀 手动触发GPS上传到高德地图...\r\n");
        GPS_ManualUpload();
    }
    else if (strcmp(clean_command, "gps_amap_status") == 0)
    {
        GPS_PrintUploadStatus();
    }
    else if (strcmp(clean_command, "gps_hengyang") == 0)
    {
        my_printf(&huart1, "📍 设置位置为衡阳市体育中心...\r\n");
        GPS_SetCustomLocationAndUpload(26.885054837223990, 112.679572502899990, 68.0);
    }
    else
    {
        // 使用已经清理过的命令字符串
        Navigation_ProcessCommand(clean_command);
    }

    memset(uart_data_buffer, 0, uart_data_len);
  }
}


